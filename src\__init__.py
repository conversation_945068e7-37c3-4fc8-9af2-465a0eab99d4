"""
Arien AI - A sophisticated AI-powered CLI terminal system.

This package provides a modular, production-ready AI assistant with support for
multiple LLM providers, function tools, and intelligent execution strategies.
"""

__version__ = "1.0.0"
__author__ = "Arien AI"
__email__ = "<EMAIL>"

# Core exports - use lazy imports to avoid circular dependencies
def get_agent():
    from src.core.agent import Agent
    return Agent

def get_exceptions():
    from src.core.exceptions import ArienError, ConfigurationError, ProviderError, ToolError
    return ArienError, ConfigurationError, ProviderError, ToolError

def get_settings_module():
    from src.config.settings import Settings, get_settings
    return Settings, get_settings

def get_providers():
    from src.providers import create_provider, get_available_providers
    return create_provider, get_available_providers

def get_tools():
    from src.tools import create_tool, get_available_tools, create_default_tools
    return create_tool, get_available_tools, create_default_tools

def get_ui():
    from src.ui.cli import CLI
    from src.ui.formatter import OutputFormatter
    return CLI, OutputFormatter

__all__ = [
    "__version__",
    "__author__",
    "__email__",
    "get_agent",
    "get_exceptions",
    "get_settings_module",
    "get_providers",
    "get_tools",
    "get_ui",
]
