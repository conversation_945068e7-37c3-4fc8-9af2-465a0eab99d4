#!/usr/bin/env python3
"""
Test script for the enhanced dropdown component.
Demonstrates all the new features and improvements.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.dropdown import (
    InteractiveDropdown, 
    DropdownConfig, 
    DropdownTheme,
    create_tool_confirmation_dropdown,
    create_simple_confirmation_dropdown,
    create_multi_choice_dropdown,
    create_themed_dropdown
)
from rich.console import Console

def test_basic_dropdown():
    """Test basic dropdown functionality."""
    print("\n🔧 Testing Basic Dropdown")
    print("=" * 50)
    
    dropdown = InteractiveDropdown()
    dropdown.add_choice("option1", "First Option", "This is the first option", "green")
    dropdown.add_choice("option2", "Second Option", "This is the second option", "blue")
    dropdown.add_choice("option3", "Third Option", "This is the third option", "yellow")
    
    result = dropdown.show("Basic Test", "Please select an option:")
    print(f"Selected: {result}")

def test_enhanced_dropdown():
    """Test enhanced dropdown with all features."""
    print("\n✨ Testing Enhanced Dropdown")
    print("=" * 50)
    
    config = DropdownConfig(
        theme=DropdownTheme.MODERN,
        show_shortcuts=True,
        show_icons=True,
        show_descriptions=True
    )
    
    dropdown = InteractiveDropdown(config=config)
    
    # Add choices with all features
    dropdown.add_choice(
        value="create",
        label="Create File",
        description="Create a new file in the project",
        style="bold green",
        icon="📄",
        shortcut="c"
    )
    dropdown.add_choice(
        value="edit",
        label="Edit File", 
        description="Edit an existing file",
        style="bold blue",
        icon="✏️",
        shortcut="e"
    )
    dropdown.add_choice(
        value="delete",
        label="Delete File",
        description="Delete a file (destructive action)",
        style="bold red", 
        icon="🗑️",
        shortcut="d"
    )
    
    dropdown.add_separator("Advanced Options")
    
    dropdown.add_choice(
        value="backup",
        label="Backup Project",
        description="Create a backup of the entire project",
        style="bold cyan",
        icon="💾",
        shortcut="b"
    )
    
    result = dropdown.show("Enhanced File Manager", "What would you like to do?")
    print(f"Selected: {result}")

def test_tool_confirmation():
    """Test tool confirmation dropdown."""
    print("\n🔧 Testing Tool Confirmation")
    print("=" * 50)
    
    tool_args = {
        "command": "rm -rf /important/files",
        "working_directory": "/home/<USER>",
        "timeout": 30
    }
    
    result = create_tool_confirmation_dropdown("bash", tool_args)
    print(f"Tool confirmation result: {result}")

def test_simple_confirmation():
    """Test simple confirmation dropdown."""
    print("\n❓ Testing Simple Confirmation")
    print("=" * 50)
    
    result = create_simple_confirmation_dropdown(
        "Are you sure you want to delete all files?",
        "⚠️ Destructive Action Warning"
    )
    print(f"Confirmation result: {result}")

def test_themed_dropdowns():
    """Test different themes."""
    print("\n🎨 Testing Different Themes")
    print("=" * 50)
    
    choices = [
        {"value": "light", "label": "Light Mode", "icon": "☀️", "shortcut": "l"},
        {"value": "dark", "label": "Dark Mode", "icon": "🌙", "shortcut": "d"},
        {"value": "auto", "label": "Auto Mode", "icon": "🔄", "shortcut": "a"}
    ]
    
    themes_to_test = [
        (DropdownTheme.DEFAULT, "Default Theme"),
        (DropdownTheme.MODERN, "Modern Theme"),
        (DropdownTheme.MINIMAL, "Minimal Theme"),
        (DropdownTheme.HIGH_CONTRAST, "High Contrast Theme"),
        (DropdownTheme.DARK, "Dark Theme")
    ]
    
    for theme, theme_name in themes_to_test:
        print(f"\nTesting {theme_name}:")
        result = create_themed_dropdown(
            f"🎨 {theme_name}",
            "Select your preferred display mode:",
            choices,
            theme
        )
        print(f"Selected: {result}")
        if result is None:  # User cancelled, break the loop
            break

def test_grouped_dropdown():
    """Test dropdown with grouped choices."""
    print("\n📁 Testing Grouped Dropdown")
    print("=" * 50)
    
    config = DropdownConfig(
        theme=DropdownTheme.MODERN,
        show_shortcuts=True,
        show_icons=True
    )
    
    dropdown = InteractiveDropdown(config=config)
    
    # File operations group
    dropdown.add_choice("new", "New File", "Create a new file", "green", "📄", "n", group="File Operations")
    dropdown.add_choice("open", "Open File", "Open an existing file", "blue", "📂", "o", group="File Operations")
    dropdown.add_choice("save", "Save File", "Save current file", "yellow", "💾", "s", group="File Operations")
    
    # Edit operations group  
    dropdown.add_choice("cut", "Cut", "Cut selected text", "red", "✂️", "x", group="Edit Operations")
    dropdown.add_choice("copy", "Copy", "Copy selected text", "cyan", "📋", "c", group="Edit Operations")
    dropdown.add_choice("paste", "Paste", "Paste from clipboard", "magenta", "📌", "v", group="Edit Operations")
    
    # Settings group
    dropdown.add_choice("prefs", "Preferences", "Open preferences", "white", "⚙️", "p", group="Settings")
    dropdown.add_choice("about", "About", "Show about dialog", "dim", "ℹ️", "a", group="Settings")
    
    result = dropdown.show("📝 Text Editor Menu", "Choose an action:")
    print(f"Selected: {result}")

def main():
    """Run all tests."""
    console = Console()
    console.print("\n🚀 Enhanced Dropdown Component Test Suite", style="bold blue")
    console.print("=" * 60, style="blue")
    
    tests = [
        ("Basic Dropdown", test_basic_dropdown),
        ("Enhanced Features", test_enhanced_dropdown), 
        ("Tool Confirmation", test_tool_confirmation),
        ("Simple Confirmation", test_simple_confirmation),
        ("Themed Dropdowns", test_themed_dropdowns),
        ("Grouped Dropdown", test_grouped_dropdown)
    ]
    
    for test_name, test_func in tests:
        try:
            console.print(f"\n▶️ Running: {test_name}", style="bold yellow")
            test_func()
            console.print(f"✅ {test_name} completed", style="green")
        except KeyboardInterrupt:
            console.print(f"\n⏹️ Test interrupted by user", style="yellow")
            break
        except Exception as e:
            console.print(f"❌ {test_name} failed: {e}", style="red")
    
    console.print("\n🏁 Test suite completed!", style="bold green")

if __name__ == "__main__":
    main()
